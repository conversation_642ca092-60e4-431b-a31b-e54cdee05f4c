{"scripts": {"dev": "bunx --bun vike dev", "build": "vike build", "preview": "vike preview", "lint": "eslint .", "shadcn": "npx shadcn@latest"}, "dependencies": {"@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-slot": "^1.2.3", "@vitejs/plugin-react": "^4.7.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.536.0", "motion": "^12.23.12", "react": "^19.1.1", "react-dom": "^19.1.1", "tailwind-merge": "^3.3.1", "tw-animate-css": "^1.3.6", "vike": "^0.4.236", "vike-react": "^0.6.5"}, "devDependencies": {"typescript": "^5.9.2", "vite": "^7.0.6", "eslint": "^9.32.0", "@eslint/js": "^9.32.0", "typescript-eslint": "^8.39.0", "globals": "^16.3.0", "eslint-plugin-react": "^7.37.5", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "tailwindcss": "^4.1.11", "@tailwindcss/vite": "^4.1.11"}, "type": "module"}