import React from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Palette, 
  Package, 
  Shirt, 
  Coffee,
  Gift,
  Tag,
  ShoppingBag,
  FileText
} from "lucide-react";
import { motion } from "framer-motion";

export default function ServicesSection() {
  const services = [
    {
      category: "Branded Merchandise & Apparel",
      icon: Shirt,
      color: "from-blue-500 to-blue-600",
      bgColor: "bg-blue-50",
      textColor: "text-blue-600",
      items: [
        "T-Shirts, Caps & Uniforms",
        "Mugs, Pens, Notebooks", 
        "Gift Items & Promotional Giveaways",
        "Lanyards, ID Cards & Name Tags"
      ]
    },
    {
      category: "Packaging & Label Printing",
      icon: Package,
      color: "from-green-500 to-green-600",
      bgColor: "bg-green-50",
      textColor: "text-green-600",
      items: [
        "Product Packaging Boxes",
        "Food & Retail Packaging",
        "Stickers & Custom Labels",
        "Branded Bags & Wrappers"
      ]
    }
  ];

  const additionalServices = [
    { icon: Palette, title: "Brand Strategy", description: "Complete brand identity development" },
    { icon: FileText, title: "Visual Identity", description: "Logo design and brand guidelines" },
    { icon: Coffee, title: "Digital Assets", description: "Social media and web graphics" },
    { icon: Gift, title: "Marketing Materials", description: "Brochures, flyers, and banners" }
  ];

  return (
    <section id="services" className="py-24 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <Badge className="bg-lime-100 text-lime-800 px-4 py-2 rounded-full mb-4">
            Our Services
          </Badge>
          <h2 className="text-3xl md:text-5xl font-bold text-gray-900 mb-6">
            Complete Branding{" "}
            <span className="bg-gradient-to-r from-lime-500 to-green-600 bg-clip-text text-transparent">
              Solutions
            </span>
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed">
            From concept to completion, we offer comprehensive branding and printing services 
            to elevate your business presence.
          </p>
        </motion.div>

        {/* Main Services */}
        <div className="grid lg:grid-cols-2 gap-8 mb-16">
          {services.map((service, index) => (
            <motion.div
              key={service.category}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: index * 0.2 }}
              viewport={{ once: true }}
            >
              <Card className="h-full border-none shadow-xl hover:shadow-2xl transition-all duration-300 overflow-hidden">
                <CardHeader className={`${service.bgColor} relative`}>
                  <div className="flex items-center space-x-4">
                    <div className={`w-16 h-16 bg-gradient-to-r ${service.color} rounded-2xl flex items-center justify-center shadow-lg`}>
                      <service.icon className="w-8 h-8 text-white" />
                    </div>
                    <div>
                      <CardTitle className="text-xl font-bold text-gray-900 leading-tight">
                        {service.category}
                      </CardTitle>
                      <div className={`w-12 h-1 ${service.color.split(' ')[0].replace('from-', 'bg-')} rounded-full mt-2`}></div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="p-8">
                  <div className="space-y-4">
                    {service.items.map((item, itemIndex) => (
                      <motion.div
                        key={item}
                        initial={{ opacity: 0, x: -20 }}
                        whileInView={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.5, delay: (index * 0.2) + (itemIndex * 0.1) }}
                        viewport={{ once: true }}
                        className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200"
                      >
                        <div className={`w-2 h-2 ${service.color.split(' ')[0].replace('from-', 'bg-')} rounded-full flex-shrink-0`}></div>
                        <span className="text-gray-700 font-medium">{item}</span>
                      </motion.div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Additional Services Grid */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-12"
        >
          <h3 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4">Additional Services</h3>
          <p className="text-gray-600 max-w-2xl mx-auto">
            We also provide comprehensive creative services to support your brand journey.
          </p>
        </motion.div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
          {additionalServices.map((service, index) => (
            <motion.div
              key={service.title}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="text-center p-6 rounded-2xl bg-white border border-gray-100 hover:shadow-lg hover:border-lime-200 transition-all duration-300"
            >
              <div className="w-12 h-12 bg-lime-100 rounded-xl flex items-center justify-center mx-auto mb-4">
                <service.icon className="w-6 h-6 text-lime-600" />
              </div>
              <h4 className="text-lg font-semibold text-gray-900 mb-2">{service.title}</h4>
              <p className="text-sm text-gray-600 leading-relaxed">{service.description}</p>
            </motion.div>
          ))}
        </div>

        {/* Call to Action */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <div className="bg-gradient-to-r from-lime-500 to-green-600 rounded-3xl p-8 md:p-12 text-white">
            <h3 className="text-2xl md:text-3xl font-bold mb-4">
              Ready to Transform Your Brand?
            </h3>
            <p className="text-lime-100 mb-8 max-w-2xl mx-auto leading-relaxed">
              Let's collaborate to create a brand identity that resonates with your audience 
              and drives your business forward.
            </p>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => document.getElementById('contact')?.scrollIntoView({ behavior: 'smooth' })}
              className="bg-white text-lime-600 font-semibold px-8 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
            >
              Start Your Project Today
            </motion.button>
          </div>
        </motion.div>
      </div>
    </section>
  );
}