import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { CheckCircle, Heart, Zap, Users, Award } from "lucide-react";
import { motion } from "framer-motion";

export default function AboutSection() {
  const values = [
    {
      icon: CheckCircle,
      title: "Integrity",
      description: "We operate with honesty and transparency, building trust every step of the way."
    },
    {
      icon: Users,
      title: "Collaboration",
      description: "We treat our clients as partners, fostering open dialogue and shared vision."
    },
    {
      icon: Award,
      title: "Creativity & Innovation",
      description: "We use imagination to build bold, standout brands."
    },
    {
      icon: Heart,
      title: "Impact",
      description: "We focus on creating lasting emotional and commercial value."
    },
    {
      icon: Zap,
      title: "Speed",
      description: "We deliver high-quality work quickly, without compromising excellence."
    }
  ];

  return (
    <section id="about" className="py-24 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <Badge className="bg-lime-100 text-lime-800 px-4 py-2 rounded-full mb-4">
            About Melchz's Designers
          </Badge>
          <h2 className="text-3xl md:text-5xl font-bold text-gray-900 mb-6">
            Crafting Brands That{" "}
            <span className="bg-gradient-to-r from-lime-500 to-green-600 bg-clip-text text-transparent">
              Resonate
            </span>
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Founded in 2022, Melchz's Designers is a full-service branding, printing, and creative 
            agency dedicated to helping businesses unlock their identity and connect with audiences.
          </p>
        </motion.div>

        <div className="grid lg:grid-cols-2 gap-16 items-center mb-16">
          {/* Story Content */}
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="space-y-6"
          >
            <div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Our Story</h3>
              <p className="text-gray-600 leading-relaxed mb-6">
                We specialize in building authentic brands from the ground up — combining market insight, 
                compelling storytelling, and innovative design to create lasting impact. With a passionate 
                team of designers, strategists, and content creators, we deliver tailored solutions in 
                brand strategy, visual identity, brand messaging, packaging, and digital brand experiences.
              </p>
              <p className="text-gray-600 leading-relaxed">
                Whether launching a new business or reimagining an existing brand, we ensure every 
                element aligns with your values and goals.
              </p>
            </div>

            <div className="grid grid-cols-2 gap-6">
              <div>
                <h4 className="text-lg font-semibold text-gray-900 mb-2">Our Vision</h4>
                <p className="text-sm text-gray-600">
                  To create meaningful brands that connect deeply with people and make a lasting cultural impact.
                </p>
              </div>
              <div>
                <h4 className="text-lg font-semibold text-gray-900 mb-2">Our Mission</h4>
                <p className="text-sm text-gray-600">
                  Transform ideas into iconic brands — blending creativity, strategy, and storytelling.
                </p>
              </div>
            </div>
          </motion.div>

          {/* Company Info */}
          <motion.div
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <Card className="p-8 border-none shadow-xl bg-gradient-to-br from-lime-50 to-green-50">
              <CardContent className="p-0 space-y-6">
                <div className="text-center">
                  <div className="w-16 h-16 bg-lime-500 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <span className="text-white text-2xl font-bold">M</span>
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-2">Company Overview</h3>
                </div>
                
                <div className="space-y-4">
                  <div className="flex justify-between items-center py-2 border-b border-gray-200">
                    <span className="text-gray-600 font-medium">Founded</span>
                    <span className="text-gray-900 font-semibold">2022</span>
                  </div>
             
                  <div className="flex justify-between items-center py-2 border-b border-gray-200">
                    <span className="text-gray-600 font-medium">Location</span>
                    <span className="text-gray-900 font-semibold">Nairobi, Kenya</span>
                  </div>
                  <div className="flex justify-between items-center py-2">
                    <span className="text-gray-600 font-medium">Specialty</span>
                    <span className="text-gray-900 font-semibold">Brand Design</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* Core Values */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-12"
        >
          <h3 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4">Our Core Values</h3>
          <p className="text-gray-600 max-w-2xl mx-auto">
            These principles guide everything we do and shape how we work with our clients.
          </p>
        </motion.div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6">
          {values.map((value, index) => (
            <motion.div
              key={value.title}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="text-center p-6 rounded-2xl bg-white border border-gray-100 hover:shadow-lg hover:border-lime-200 transition-all duration-300"
            >
              <div className="w-12 h-12 bg-lime-100 rounded-xl flex items-center justify-center mx-auto mb-4">
                <value.icon className="w-6 h-6 text-lime-600" />
              </div>
              <h4 className="text-lg font-semibold text-gray-900 mb-2">{value.title}</h4>
              <p className="text-sm text-gray-600 leading-relaxed">{value.description}</p>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}